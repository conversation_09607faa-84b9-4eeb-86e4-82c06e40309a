import { Gamepad2, Gift, CreditCard, Smartphone, Music, Video } from "lucide-react"

export default function Categories() {
  const categories = [
    {
      icon: <Gamepad2 className="h-8 w-8" />,
      title: "شحن ببجي",
      description: "UC و BP بأفضل الأسعار",
    },
    {
      icon: <Gift className="h-8 w-8" />,
      title: "بطاقات آيتونز",
      description: "بطاقات رقمية فورية",
    },
    {
      icon: <CreditCard className="h-8 w-8" />,
      title: "بطاقات جوجل بلاي",
      description: "شحن متجر جوجل",
    },
    {
      icon: <Smartphone className="h-8 w-8" />,
      title: "شحن الجوال",
      description: "رصيد لجميع الشبكات",
    },
    {
      icon: <Music className="h-8 w-8" />,
      title: "اشتراكات الموسيقى",
      description: "سبوتيفاي وأبل ميوزك",
    },
    {
      icon: <Video className="h-8 w-8" />,
      title: "اشتراكات الفيديو",
      description: "نتفليكس وشاهد",
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">فئات المنتجات</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">اختر من مجموعة واسعة من المنتجات الرقمية والخدمات</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 md:gap-6">
          {categories.map((category, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-slate-100 cursor-pointer group"
            >
              <div className="text-slate-900 group-hover:text-yellow-500 transition-colors duration-300 mb-4 flex justify-center">
                {category.icon}
              </div>
              <h3 className="font-bold text-slate-900 mb-2 text-center text-sm md:text-base">{category.title}</h3>
              <p className="text-slate-600 text-xs md:text-sm text-center">{category.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
