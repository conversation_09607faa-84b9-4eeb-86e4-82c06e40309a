import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"

export default function Hero() {
  return (
    <section className="bg-yellow-400 py-16 md:py-24">
      <div className="container mx-auto px-4 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">أفضل متجر للشحن الفوري</h1>
          <p className="text-xl md:text-2xl text-slate-800 mb-8">
            شحن ألعابك المفضلة وبطاقاتك الرقمية بأسرع وقت وأفضل الأسعار
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="bg-slate-900 hover:bg-slate-800 text-white px-8 py-4 text-lg font-semibold">
              تسوق الآن
              <ArrowLeft className="mr-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-slate-900 text-slate-900 hover:bg-slate-900 hover:text-white px-8 py-4 text-lg font-semibold"
            >
              تصفح الفئات
            </Button>
          </div>
          <p className="text-sm text-slate-700 mt-4">✅ شحن فوري خلال دقائق | ✅ دعم فني 24/7 | ✅ أسعار منافسة</p>
        </div>
      </div>
    </section>
  )
}
