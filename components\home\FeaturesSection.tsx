export function FeaturesSection() {
  const features = [
    { icon: "⚡", title: "شحن فوري", desc: "احصل على منتجك خلال دقائق" },
    { icon: "💰", title: "أسعار منافسة", desc: "أفضل الأسعار في السوق" },
    { icon: "🔒", title: "دفع آمن", desc: "معاملات محمية 100%" },
    { icon: "🎧", title: "دعم 24/7", desc: "فريق دعم متاح دائماً" },
  ]

  return (
    <div className="bg-slate-800/50 backdrop-blur-xl rounded-3xl p-8 border border-slate-700/50">
      <h3 className="text-2xl font-bold text-yellow-400 mb-6">لماذا تختار رايه شوب؟</h3>
      <div className="space-y-4">
        {features.map((feature, idx) => (
          <div key={idx} className="flex items-center gap-4 p-4 rounded-xl hover:bg-white/5 transition-colors">
            <div className="text-2xl">{feature.icon}</div>
            <div>
              <h4 className="font-semibold text-white">{feature.title}</h4>
              <p className="text-slate-400 text-sm">{feature.desc}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
