"use client"

import { User, CreditCard, Home, HelpCircle, Settings, Wallet, Store } from "lucide-react"

interface MobileNavigationProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export function MobileNavigation({ activeTab, onTabChange }: MobileNavigationProps) {
  const navItems = [
    { id: "profile", icon: <User className="h-5 w-5" />, label: "حسابي" },
    { id: "products", icon: <Store className="h-5 w-5" />, label: "المنتجات" },
    {
      id: "home",
      icon: <Home className="h-6 w-6" />,
      label: "",
      center: true,
    },
    { id: "wallet", icon: <Wallet className="h-5 w-5" />, label: "المحفظة" },
    { id: "support", icon: <Settings className="h-5 w-5" />, label: "الدعم" },
  ]

  return (
    <nav className="lg:hidden fixed bottom-6 left-1/2 w-[calc(100%-2rem)] max-w-sm -translate-x-1/2 z-40">
      <div className="bg-slate-800/80 backdrop-blur-2xl rounded-3xl px-6 py-4 shadow-2xl border border-slate-700/50">
        <div className="flex items-center justify-around">
          {navItems.map(({ id, icon, label, center }) =>
            center ? (
              <button
                key={id}
                onClick={() => onTabChange(id)}
                className="flex flex-col items-center p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg hover:scale-110 transition-all duration-300 text-slate-900"
              >
                {icon}
              </button>
            ) : (
              <button
                key={id}
                onClick={() => onTabChange(id)}
                className={`flex flex-col items-center space-y-1 p-3 rounded-2xl transition-all duration-300 hover:scale-110 ${
                  activeTab === id
                    ? "bg-white/20 text-yellow-400 shadow-lg"
                    : "text-slate-400 hover:text-white hover:bg-white/10"
                }`}
              >
                {icon}
                <span className="text-xs font-medium">{label}</span>
              </button>
            ),
          )}
        </div>
      </div>
    </nav>
  )
}
