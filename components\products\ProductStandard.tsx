"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Star, 
  Info, 
  MessageSquare,
  Package,
  Gamepad2
} from "lucide-react"

interface ProductOffer {
  id: string
  label: string
  value: string
  price: number
  originalPrice?: number
  discount?: number
}

interface ProductRegion {
  id: string
  label: string
  value: string
}

interface ProductStandardProps {
  product: {
    id: string
    title: string
    shortDescription: string
    fullDescription: string
    priceRange: string
    image: string
    category: string
    rating: number
    reviewsCount: number
    offers: ProductOffer[]
    regions?: ProductRegion[]
    hasRegion: boolean
    inStock: boolean
    features: string[]
    additionalInfo: Record<string, string>
  }
}

export function ProductStandard({ product }: ProductStandardProps) {
  const [selectedOffer, setSelectedOffer] = useState<string>("")
  const [selectedRegion, setSelectedRegion] = useState<string>("")
  const [quantity, setQuantity] = useState(1)
  const [isLoading, setIsLoading] = useState(false)

  // ## Get selected offer details for price calculation
  const currentOffer = product.offers.find(offer => offer.id === selectedOffer)
  const totalPrice = currentOffer ? currentOffer.price * quantity : 0

  const handleAddToCart = async () => {
    if (!selectedOffer) return
    
    setIsLoading(true)
    try {
      // ## Mock add to cart handler - will be replaced with Supabase cart operations
      console.log("Add to cart:", {
        productId: product.id,
        offerId: selectedOffer,
        regionId: selectedRegion,
        quantity
      })

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // ## TODO: Integrate with cart state management and Supabase
      alert("تم إضافة المنتج إلى السلة بنجاح!")

    } catch (error) {
      console.error("Error adding to cart:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const incrementQuantity = () => setQuantity(prev => Math.min(prev + 1, 10))
  const decrementQuantity = () => setQuantity(prev => Math.max(prev - 1, 1))

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 max-w-6xl">
        {/* Product Header */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Product Image */}
          <div className="space-y-4">
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="aspect-square bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl flex items-center justify-center">
                  {product.image ? (
                    <img 
                      src={product.image} 
                      alt={product.title}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  ) : (
                    <Gamepad2 className="h-24 w-24 text-slate-400" />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Product Info & Purchase */}
          <div className="space-y-6">
            {/* Title & Category */}
            <div>
              <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 mb-3">
                {product.category}
              </Badge>
              <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">
                {product.title}
              </h1>
              <p className="text-slate-300 text-lg">
                {product.shortDescription}
              </p>
            </div>

            {/* Rating & Reviews */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    className={cn(
                      "h-5 w-5",
                      i < product.rating ? "text-yellow-400 fill-current" : "text-slate-600"
                    )} 
                  />
                ))}
              </div>
              <span className="text-slate-400">
                ({product.reviewsCount} تقييم)
              </span>
            </div>

            {/* Price Range */}
            <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
              <div className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                {product.priceRange} ج.س
              </div>
              <div className="text-slate-400 text-sm mt-1">
                الأسعار تبدأ من
              </div>
            </div>

            {/* Offer Selector */}
            <div className="space-y-3">
              <Label className="text-slate-300 font-medium">اختر العرض:</Label>
              <Select value={selectedOffer} onValueChange={setSelectedOffer}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600/50 text-white">
                  <SelectValue placeholder="اختر العرض المناسب" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  {product.offers.map((offer) => (
                    <SelectItem key={offer.id} value={offer.id} className="text-white">
                      <div className="flex justify-between items-center w-full">
                        <span>{offer.label}</span>
                        <div className="flex items-center gap-2">
                          {offer.originalPrice && (
                            <span className="text-slate-400 line-through text-sm">
                              {offer.originalPrice} ج.س
                            </span>
                          )}
                          <span className="font-bold text-yellow-400">
                            {offer.price} ج.س
                          </span>
                          {offer.discount && (
                            <Badge className="bg-red-500 text-white text-xs">
                              -{offer.discount}%
                            </Badge>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Region Selector */}
            {product.hasRegion && product.regions && (
              <div className="space-y-3">
                <Label className="text-slate-300 font-medium">اختر المتجر/المنطقة:</Label>
                <Select value={selectedRegion} onValueChange={setSelectedRegion}>
                  <SelectTrigger className="bg-slate-700/50 border-slate-600/50 text-white">
                    <SelectValue placeholder="اختر المتجر" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    {product.regions.map((region) => (
                      <SelectItem key={region.id} value={region.id} className="text-white">
                        {region.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Quantity Selector */}
            <div className="space-y-3">
              <Label className="text-slate-300 font-medium">الكمية:</Label>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={decrementQuantity}
                  disabled={quantity <= 1}
                  className="bg-slate-700/50 border-slate-600/50 hover:bg-slate-600/50"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Input
                  value={quantity}
                  readOnly
                  className="w-20 text-center bg-slate-700/50 border-slate-600/50 text-white"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={incrementQuantity}
                  disabled={quantity >= 10}
                  className="bg-slate-700/50 border-slate-600/50 hover:bg-slate-600/50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Total Price */}
            {currentOffer && (
              <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">المجموع:</span>
                  <span className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                    {totalPrice} ج.س
                  </span>
                </div>
              </div>
            )}

            {/* Add to Cart Button */}
            <Button
              onClick={handleAddToCart}
              disabled={!selectedOffer || !product.inStock || isLoading}
              className="w-full h-14 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold text-lg"
            >
              <ShoppingCart className="h-5 w-5 ml-2" />
              {isLoading ? "جاري الإضافة..." : "أضف إلى السلة"}
            </Button>

            {!product.inStock && (
              <div className="text-center text-red-400 font-medium">
                غير متوفر حالياً
              </div>
            )}
          </div>
        </div>

        {/* Product Details Tabs */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-6">
            <Tabs defaultValue="description" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-slate-700/50 rounded-xl p-1">
                <TabsTrigger
                  value="description"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-yellow-400 data-[state=active]:to-orange-500 data-[state=active]:text-slate-900 rounded-lg"
                >
                  <MessageSquare className="h-4 w-4 ml-1" />
                  الوصف
                </TabsTrigger>

              </TabsList>

              <TabsContent value="description" className="mt-6">
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-white mb-4">وصف المنتج</h3>
                  <div className="text-slate-300 leading-relaxed">
                    {product.fullDescription}
                  </div>

                  {product.features && product.features.length > 0 && (
                    <div className="mt-6">
                      <h4 className="text-lg font-semibold text-white mb-3">المميزات:</h4>
                      <ul className="space-y-2">
                        {product.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-slate-300">
                            <div className="w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </TabsContent>


            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
