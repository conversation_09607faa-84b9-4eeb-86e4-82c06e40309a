"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import { 
  Zap, 
  Gamepad2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Shield,
  Star,
  Copy,
  Download
} from "lucide-react"

interface ProductPack {
  id: string
  name: string
  amount: string
  price: number
  originalPrice?: number
  discount?: number
  popular?: boolean
}

interface ProductServer {
  id: string
  name: string
  region: string
}

interface InstantReceipt {
  id: string
  status: "success" | "pending" | "failed"
  transactionId: string
  amount: string
  timestamp: string
  details?: Record<string, any>
}

interface ProductInstantProps {
  product: {
    id: string
    title: string
    shortDescription: string
    image: string
    category: string
    rating: number
    reviewsCount: number
    packs: ProductPack[]
    servers: ProductServer[]
    instructions: string[]
    estimatedTime: string
    features: string[]
  }
}

export function ProductInstant({ product }: ProductInstantProps) {
  const [userId, setUserId] = useState("")
  const [selectedServer, setSelectedServer] = useState("")
  const [selectedPack, setSelectedPack] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [receipt, setReceipt] = useState<InstantReceipt | null>(null)
  const [error, setError] = useState("")

  // ## Get selected pack details for price display
  const currentPack = product.packs.find(pack => pack.id === selectedPack)

  const handleInstantCharge = async () => {
    if (!userId || !selectedServer || !selectedPack) {
      setError("يرجى ملء جميع الحقول المطلوبة")
      return
    }

    setIsLoading(true)
    setError("")
    
    try {
      // ## Mock API client for instant products - will be replaced with real API
      const mockApiClient = {
        chargeInstant: async (data: { userId: string; server: string; pack: string }) => {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 2000))

          return {
            id: `receipt_${Date.now()}`,
            status: "success" as const,
            transactionId: `TXN_${Date.now()}`,
            amount: product.packs.find(p => p.id === data.pack)?.amount || "",
            timestamp: new Date().toISOString(),
            details: data
          }
        }
      }

      // ## Call instant charge API - will be replaced with real Supabase/API integration
      const result = await mockApiClient.chargeInstant({
        userId,
        server: selectedServer,
        pack: selectedPack
      })
      setReceipt(result)
    } catch (error) {
      console.error("Instant charge error:", error)
      setError("حدث خطأ أثناء عملية الشحن. يرجى المحاولة مرة أخرى.")
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="relative z-10 container mx-auto px-4 pt-32 lg:pt-36 pb-32 max-w-4xl">
        {/* Product Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Badge className="bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 text-lg">
              <Zap className="h-4 w-4 ml-1" />
              شحن فوري
            </Badge>
          </div>
          
          <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">
            {product.title}
          </h1>
          <p className="text-slate-300 text-lg max-w-2xl mx-auto">
            {product.shortDescription}
          </p>

          {/* Rating & Time */}
          <div className="flex items-center justify-center gap-6 mt-4">
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <Star 
                  key={i} 
                  className={cn(
                    "h-4 w-4",
                    i < product.rating ? "text-yellow-400 fill-current" : "text-slate-600"
                  )} 
                />
              ))}
              <span className="text-slate-400 text-sm mr-2">
                ({product.reviewsCount})
              </span>
            </div>
            <div className="flex items-center gap-1 text-green-400">
              <Clock className="h-4 w-4" />
              <span className="text-sm">{product.estimatedTime}</span>
            </div>
          </div>
        </div>

        {/* Product Image */}
        <div className="mb-8 flex justify-center">
          <div className="w-full max-w-md">
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="aspect-square bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl flex items-center justify-center overflow-hidden">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.title}
                      className="w-full h-full object-cover rounded-xl"
                    />
                  ) : (
                    <Gamepad2 className="h-24 w-24 text-slate-400" />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Form */}
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-8">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Gamepad2 className="h-6 w-6 text-yellow-400" />
              معلومات الشحن
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* User ID Input */}
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">
                الأيدي الخاص بك (ID) *
              </Label>
              <Input
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                placeholder="أدخل الأيدي الخاص بك"
                className="bg-slate-700/50 border-slate-600/50 text-white placeholder:text-slate-400"
              />
              <div className="text-slate-400 text-sm">
                تأكد من إدخال الأيدي بشكل صحيح لتجنب فقدان الشحنة
              </div>
            </div>

            {/* Server Selector */}
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">السيرفر *</Label>
              <Select value={selectedServer} onValueChange={setSelectedServer}>
                <SelectTrigger className="bg-slate-700/50 border-slate-600/50 text-white">
                  <SelectValue placeholder="اختر السيرفر" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  {product.servers.map((server) => (
                    <SelectItem key={server.id} value={server.id} className="text-white">
                      {server.name} - {server.region}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Pack Selector */}
            <div className="space-y-3">
              <Label className="text-slate-300 font-medium">اختر العرض *</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {product.packs.map((pack) => (
                  <div
                    key={pack.id}
                    onClick={() => setSelectedPack(pack.id)}
                    className={cn(
                      "relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300",
                      selectedPack === pack.id
                        ? "border-yellow-400 bg-yellow-400/10"
                        : "border-slate-600/50 bg-slate-700/30 hover:border-slate-500"
                    )}
                  >
                    {pack.popular && (
                      <Badge className="absolute -top-2 right-4 bg-gradient-to-r from-red-500 to-red-600 text-white">
                        الأكثر شعبية
                      </Badge>
                    )}
                    
                    <div className="text-white font-bold text-lg mb-1">
                      {pack.name}
                    </div>
                    <div className="text-slate-300 text-sm mb-2">
                      {pack.amount}
                    </div>
                    <div className="flex items-center gap-2">
                      {pack.originalPrice && (
                        <span className="text-slate-400 line-through text-sm">
                          {pack.originalPrice} ج.س
                        </span>
                      )}
                      <span className="text-yellow-400 font-bold">
                        {pack.price} ج.س
                      </span>
                      {pack.discount && (
                        <Badge className="bg-red-500 text-white text-xs">
                          -{pack.discount}%
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Error Display */}
            {error && (
              <Alert className="border-red-500/50 bg-red-500/10">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {/* Total Price */}
            {currentPack && (
              <div className="bg-slate-700/30 rounded-xl p-4 border border-slate-600/30">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">المجموع:</span>
                  <span className="text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                    {currentPack.price} ج.س
                  </span>
                </div>
              </div>
            )}

            {/* Instant Charge Button */}
            <Button
              onClick={handleInstantCharge}
              disabled={!userId || !selectedServer || !selectedPack || isLoading}
              className="w-full h-14 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold text-lg"
            >
              <Zap className="h-5 w-5 ml-2" />
              {isLoading ? "جاري الشحن..." : "اشحن الآن"}
            </Button>
          </CardContent>
        </Card>

        {/* Receipt/Confirmation */}
        {receipt && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-8">
            <CardHeader>
              <CardTitle className="text-white text-xl flex items-center gap-2">
                {receipt.status === "success" ? (
                  <CheckCircle className="h-6 w-6 text-green-400" />
                ) : receipt.status === "pending" ? (
                  <Clock className="h-6 w-6 text-yellow-400" />
                ) : (
                  <AlertCircle className="h-6 w-6 text-red-400" />
                )}
                إيصال الشحن
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                  <div className="text-slate-400 text-sm mb-1">رقم المعاملة</div>
                  <div className="flex items-center gap-2">
                    <span className="text-white font-mono">{receipt.transactionId}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(receipt.transactionId)}
                      className="h-6 w-6 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                  <div className="text-slate-400 text-sm mb-1">المبلغ المشحون</div>
                  <div className="text-white font-bold">{receipt.amount}</div>
                </div>

                <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                  <div className="text-slate-400 text-sm mb-1">الحالة</div>
                  <Badge className={cn(
                    receipt.status === "success" ? "bg-green-500" :
                    receipt.status === "pending" ? "bg-yellow-500" : "bg-red-500"
                  )}>
                    {receipt.status === "success" ? "تم بنجاح" :
                     receipt.status === "pending" ? "قيد المعالجة" : "فشل"}
                  </Badge>
                </div>

                <div className="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                  <div className="text-slate-400 text-sm mb-1">التاريخ والوقت</div>
                  <div className="text-white text-sm">
                    {new Date(receipt.timestamp).toLocaleString('ar-EG')}
                  </div>
                </div>
              </div>

              {receipt.status === "success" && (
                <Alert className="border-green-500/50 bg-green-500/10">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription className="text-green-400">
                    تم شحن حسابك بنجاح! يمكنك الآن الاستمتاع باللعب.
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(JSON.stringify(receipt, null, 2))}
                  className="bg-slate-700/50 border-slate-600/50 hover:bg-slate-600/50"
                >
                  <Download className="h-4 w-4 ml-1" />
                  حفظ الإيصال
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setReceipt(null)}
                  className="bg-slate-700/50 border-slate-600/50 hover:bg-slate-600/50"
                >
                  شحن جديد
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions & Features */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Instructions */}
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-400" />
                تعليمات مهمة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {product.instructions.map((instruction, index) => (
                  <li key={index} className="flex items-start gap-2 text-slate-300">
                    <div className="w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-sm leading-relaxed">{instruction}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Features */}
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white text-lg flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-400" />
                مميزات الخدمة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-slate-300">
                    <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                    <span className="text-sm leading-relaxed">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
  )
}
