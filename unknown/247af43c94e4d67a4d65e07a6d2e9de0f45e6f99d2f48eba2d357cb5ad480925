"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ImageUpload } from "@/components/profile/ImageUpload"
import { 
  User, 
  Mail, 
  Phone, 
  Save, 
  X,
  Eye,
  EyeOff,
  Lock
} from "lucide-react"

interface UserProfile {
  id: string
  accountId: string
  displayName: string
  firstName: string
  lastName: string
  email: string
  phone: string
  avatarUrl?: string
  walletBalance: {
    sdg: number
    egp: number
  }
  ordersCount: number
  joinDate: string
  lastLogin: string
  isVerified: boolean
}

interface EditProfileModalProps {
  isOpen: boolean
  onClose: () => void
  user: UserProfile
  onUpdateProfile: (data: Partial<UserProfile>) => Promise<void>
  isLoading?: boolean
}

export function EditProfileModal({ 
  isOpen, 
  onClose, 
  user, 
  onUpdateProfile, 
  isLoading = false 
}: EditProfileModalProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [profileImage, setProfileImage] = useState<string | null>(user.avatarUrl || null)
  
  // Form state
  const [formData, setFormData] = useState({
    firstName: user.firstName,
    lastName: user.lastName,
    displayName: user.displayName,
    email: user.email,
    phone: user.phone,
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleImageUpload = (imageUrl: string) => {
    setProfileImage(imageUrl)
  }

  const handleSaveProfile = async () => {
    try {
      // Validate passwords if changing
      if (formData.newPassword && formData.newPassword !== formData.confirmPassword) {
        throw new Error("كلمات المرور غير متطابقة")
      }

      // Prepare update data
      const updateData: Partial<UserProfile> = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        displayName: formData.displayName,
        email: formData.email,
        phone: formData.phone,
        avatarUrl: profileImage || undefined
      }

      await onUpdateProfile(updateData)
      
      // Clear password fields
      setFormData(prev => ({
        ...prev,
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      }))
      
      onClose()
    } catch (error) {
      console.error("Profile update error:", error)
    }
  }

  const handleCancel = () => {
    // Reset form data
    setFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      displayName: user.displayName,
      email: user.email,
      phone: user.phone,
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    })
    setProfileImage(user.avatarUrl || null)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-slate-800/95 border-slate-700/50 backdrop-blur-xl text-white max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-center flex items-center justify-center gap-2">
            <User className="h-6 w-6 text-yellow-400" />
            تعديل الملف الشخصي
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 p-2">
          {/* Profile Picture Section */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <Avatar className="h-24 w-24 border-4 border-yellow-400/20">
                <AvatarImage src={profileImage || user.avatarUrl} alt={user.displayName} />
                <AvatarFallback className="bg-gradient-to-br from-yellow-400 to-orange-500 text-slate-900 text-2xl font-bold">
                  {user.displayName.charAt(0)}
                </AvatarFallback>
              </Avatar>
            </div>
            <ImageUpload onImageUpload={handleImageUpload} currentImage={profileImage} />
          </div>

          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-400 flex items-center gap-2">
              <User className="h-5 w-5" />
              المعلومات الشخصية
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-slate-300 font-medium">الاسم الأول</Label>
                <Input
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400"
                  placeholder="أدخل الاسم الأول"
                />
              </div>
              
              <div className="space-y-2">
                <Label className="text-slate-300 font-medium">الاسم الأخير</Label>
                <Input
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400"
                  placeholder="أدخل الاسم الأخير"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">الاسم المعروض</Label>
              <Input
                value={formData.displayName}
                onChange={(e) => handleInputChange("displayName", e.target.value)}
                className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400"
                placeholder="أدخل الاسم المعروض"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-400 flex items-center gap-2">
              <Mail className="h-5 w-5" />
              معلومات الاتصال
            </h3>
            
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">البريد الإلكتروني</Label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400"
                placeholder="أدخل البريد الإلكتروني"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">رقم الهاتف</Label>
              <Input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400"
                placeholder="أدخل رقم الهاتف"
              />
            </div>
          </div>

          {/* Password Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-yellow-400 flex items-center gap-2">
              <Lock className="h-5 w-5" />
              تغيير كلمة المرور (اختياري)
            </h3>
            
            <div className="space-y-2">
              <Label className="text-slate-300 font-medium">كلمة المرور الحالية</Label>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  value={formData.currentPassword}
                  onChange={(e) => handleInputChange("currentPassword", e.target.value)}
                  className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400 pr-10"
                  placeholder="أدخل كلمة المرور الحالية"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 text-slate-400 hover:text-white"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-slate-300 font-medium">كلمة المرور الجديدة</Label>
                <div className="relative">
                  <Input
                    type={showNewPassword ? "text" : "password"}
                    value={formData.newPassword}
                    onChange={(e) => handleInputChange("newPassword", e.target.value)}
                    className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400 pr-10"
                    placeholder="أدخل كلمة المرور الجديدة"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0 text-slate-400 hover:text-white"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-slate-300 font-medium">تأكيد كلمة المرور</Label>
                <Input
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                  className="bg-slate-700/50 border-slate-600/50 text-white focus:border-yellow-400"
                  placeholder="أعد إدخال كلمة المرور"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              onClick={handleSaveProfile}
              disabled={isLoading}
              className="flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-medium"
            >
              <Save className="h-4 w-4 ml-2" />
              {isLoading ? "جاري الحفظ..." : "حفظ التغييرات"}
            </Button>
            
            <Button
              onClick={handleCancel}
              variant="outline"
              className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700/50"
            >
              <X className="h-4 w-4 ml-2" />
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
