import { Search, CreditCard, Zap, CheckCircle } from "lucide-react"

export default function HowItWorks() {
  const steps = [
    {
      icon: <Search className="h-12 w-12" />,
      title: "اختر المنتج",
      description: "تصفح واختر المنتج المطلوب من مجموعتنا الواسعة",
    },
    {
      icon: <CreditCard className="h-12 w-12" />,
      title: "ادفع بأمان",
      description: "ادفع بطريقة آمنة عبر وسائل الدفع المتعددة",
    },
    {
      icon: <Zap className="h-12 w-12" />,
      title: "شحن فوري",
      description: "احصل على منتجك خلال دقائق معدودة",
    },
    {
      icon: <CheckCircle className="h-12 w-12" />,
      title: "استمتع",
      description: "استمتع بمنتجك واللعب مع الأصدقاء",
    },
  ]

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">كيف يعمل الموقع؟</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            عملية بسيطة وسريعة للحصول على ما تريد في خطوات معدودة
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="text-center relative">
              {/* Step Number */}
              <div className="absolute -top-4 -right-4 bg-yellow-400 text-slate-900 rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">
                {index + 1}
              </div>

              {/* Icon */}
              <div className="bg-sky-100 rounded-full p-6 w-24 h-24 mx-auto mb-6 flex items-center justify-center text-slate-900">
                {step.icon}
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-slate-900 mb-3">{step.title}</h3>
              <p className="text-slate-600 leading-relaxed">{step.description}</p>

              {/* Connector Line (hidden on last item) */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-12 left-full w-full h-0.5 bg-slate-200 -translate-x-1/2" />
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
