import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Facebook, Twitter, Instagram, Youtube, Mail, Phone, MapPin } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-white">
      {/* Newsletter Section */}
      <div className="bg-orange-500 py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="text-center md:text-right">
              <h3 className="text-2xl font-bold mb-2">اشترك في النشرة الإخبارية</h3>
              <p className="text-orange-100">احصل على أحدث العروض والخصومات الحصرية</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
              <Input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="bg-white text-slate-900 placeholder:text-slate-500 min-w-[250px]"
              />
              <Button className="bg-slate-900 hover:bg-slate-800 whitespace-nowrap">اشترك الآن</Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div>
              <h3 className="text-2xl font-bold text-yellow-400 mb-4">رايه شوب</h3>
              <p className="text-slate-300 mb-6 leading-relaxed">
                متجرك الموثوق للبطاقات الرقمية وشحن الألعاب بأفضل الأسعار وأسرع خدمة في المنطقة.
              </p>
              <div className="flex gap-4">
                <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                  <Facebook className="h-6 w-6" />
                </a>
                <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                  <Twitter className="h-6 w-6" />
                </a>
                <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                  <Instagram className="h-6 w-6" />
                </a>
                <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                  <Youtube className="h-6 w-6" />
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-4">روابط سريعة</h4>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    الرئيسية
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    المتجر
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    العروض
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    من نحن
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    اتصل بنا
                  </a>
                </li>
              </ul>
            </div>

            {/* Categories */}
            <div>
              <h4 className="text-lg font-semibold mb-4">الفئات</h4>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    شحن ببجي
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    بطاقات آيتونز
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    بطاقات جوجل بلاي
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    شحن الجوال
                  </a>
                </li>
                <li>
                  <a href="#" className="text-slate-300 hover:text-yellow-400 transition-colors">
                    الاشتراكات
                  </a>
                </li>
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold mb-4">تواصل معنا</h4>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-yellow-400" />
                  <span className="text-slate-300">+966 50 123 4567</span>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-yellow-400" />
                  <span className="text-slate-300"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-yellow-400" />
                  <span className="text-slate-300">الرياض، المملكة العربية السعودية</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-slate-800 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-slate-400 text-sm">© 2024 رايه شوب. جميع الحقوق محفوظة.</div>
            <div className="flex gap-6 text-sm">
              <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                سياسة الخصوصية
              </a>
              <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                الشروط والأحكام
              </a>
              <a href="#" className="text-slate-400 hover:text-yellow-400 transition-colors">
                سياسة الاسترداد
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
