"use client"

import { Grid3X3, ShoppingBag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"

interface AppHeaderProps {
  onMenuOpen: () => void
}

export function AppHeader({ onMenuOpen }: AppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 flex items-center justify-between p-4 lg:p-6 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-2xl">
      <Button
        variant="ghost"
        size="icon"
        className="text-white hover:bg-white/10 transition-all duration-300 hover:scale-110"
        onClick={onMenuOpen}
      >
        <Grid3X3 className="h-6 w-6" />
      </Button>

      <div className="flex items-center justify-center">
        {/* Logo will be inserted here once provided */}
      </div>

      <Button
        variant="ghost"
        size="icon"
        className="text-white bg-gradient-to-r from-pink-500 to-rose-500 rounded-full hover:scale-110 transition-all duration-300 shadow-lg"
      >
        <ShoppingBag className="h-5 w-5 lg:h-6 lg:w-6" />
      </Button>
    </header>
  )
}
