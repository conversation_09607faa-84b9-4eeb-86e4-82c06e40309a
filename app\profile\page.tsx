"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ProfilePage } from "@/components/pages/ProfilePage"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"

// ## Mock user data - will be replaced with Supabase auth and user queries
const mockUser = {
  id: "user_123456",
  accountId: "RYS_789012",
  displayName: "أحمد محمد",
  firstName: "أحمد",
  lastName: "محمد",
  email: "<EMAIL>",
  phone: "+************",
  avatarUrl: "",
  walletBalance: {
    sdg: 15750,
    egp: 2340
  },
  ordersCount: 12,
  joinDate: "2024-01-15T10:30:00Z",
  lastLogin: "2024-06-25T14:20:00Z",
  isVerified: true
}

export default function Profile() {
  const router = useRouter()
  const [user, setUser] = useState(mockUser)
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("profile")

  // ## Handle profile update - will be replaced with Supabase user update
  const handleUpdateProfile = async (data: Partial<typeof mockUser>) => {
    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Update local state
      setUser(prev => ({ ...prev, ...data }))
      
      console.log("Profile updated:", data)
      // ## Will integrate with Supabase user table updates
      
    } catch (error) {
      console.error("Profile update error:", error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  // ## Handle logout - will be replaced with Supabase auth signOut
  const handleLogout = async () => {
    try {
      // ## Will call Supabase auth.signOut()
      console.log("Logging out...")
      
      // Redirect to home page
      router.push("/")
    } catch (error) {
      console.error("Logout error:", error)
    }
  }

  // ## Navigate to wallet page
  const handleNavigateToWallet = () => {
    router.push("/wallet")
  }

  // ## Navigate to orders page - will be created later
  const handleNavigateToOrders = () => {
    router.push("/orders")
  }

  // Navigation handler for bottom navigation
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "products") {
      router.push("/products")
    } else if (tab === "home") {
      router.push("/")
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="relative">
      <ProfilePage
        user={user}
        onUpdateProfile={handleUpdateProfile}
        onLogout={handleLogout}
        onNavigateToWallet={handleNavigateToWallet}
        onNavigateToOrders={handleNavigateToOrders}
      />
      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
