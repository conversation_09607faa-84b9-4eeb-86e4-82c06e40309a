"use client"

import { useState, useEffect } from "react"
import { WalletData, Currency, Transaction } from "@/lib/types"
import { mockWalletData } from "@/lib/data/mockWalletData"

// ## Custom hook for wallet data management
// ## This will be replaced with Supabase integration
export function useWalletData(userId?: string) {
  const [walletData, setWalletData] = useState<WalletData>(mockWalletData)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // ## Fetch wallet data from Supabase
  const fetchWalletData = async () => {
    if (!userId) return

    setIsLoading(true)
    setError(null)

    try {
      // ## TODO: Replace with actual Supabase queries
      // const { data: balances } = await supabase
      //   .from('user_wallets')
      //   .select('*')
      //   .eq('user_id', userId)
      
      // const { data: transactions } = await supabase
      //   .from('wallet_transactions')
      //   .select('*')
      //   .eq('user_id', userId)
      //   .order('created_at', { ascending: false })
      //   .limit(50)

      // const { data: preferences } = await supabase
      //   .from('user_preferences')
      //   .select('preferred_currency')
      //   .eq('user_id', userId)
      //   .single()

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // For now, return mock data
      setWalletData(mockWalletData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch wallet data')
    } finally {
      setIsLoading(false)
    }
  }

  // ## Update currency preference in Supabase
  const updateCurrencyPreference = async (currency: Currency) => {
    if (!userId) return

    try {
      // ## TODO: Replace with Supabase update
      // await supabase
      //   .from('user_preferences')
      //   .upsert({ 
      //     user_id: userId, 
      //     preferred_currency: currency 
      //   })

      setWalletData(prev => ({
        ...prev,
        selectedCurrency: currency
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update currency preference')
    }
  }

  // ## Add new transaction (for deposits, purchases, etc.)
  const addTransaction = async (transaction: Omit<Transaction, 'id' | 'date'>) => {
    if (!userId) return

    try {
      // ## TODO: Replace with Supabase insert
      // const { data } = await supabase
      //   .from('wallet_transactions')
      //   .insert({
      //     user_id: userId,
      //     ...transaction,
      //     created_at: new Date().toISOString()
      //   })
      //   .select()
      //   .single()

      const newTransaction: Transaction = {
        ...transaction,
        id: `txn_${Date.now()}`,
        date: new Date()
      }

      setWalletData(prev => ({
        ...prev,
        transactions: [newTransaction, ...prev.transactions]
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add transaction')
    }
  }

  // ## Update wallet balance after transaction
  const updateBalance = async (currency: Currency, amount: number, operation: 'add' | 'subtract') => {
    if (!userId) return

    try {
      // ## TODO: Replace with Supabase update
      // await supabase.rpc('update_wallet_balance', {
      //   p_user_id: userId,
      //   p_currency: currency,
      //   p_amount: operation === 'add' ? amount : -amount
      // })

      setWalletData(prev => ({
        ...prev,
        balances: prev.balances.map(balance => 
          balance.currency === currency
            ? {
                ...balance,
                amount: operation === 'add' 
                  ? balance.amount + amount 
                  : balance.amount - amount,
                lastUpdated: new Date()
              }
            : balance
        )
      }))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update balance')
    }
  }

  // ## Set up real-time subscriptions
  useEffect(() => {
    if (!userId) return

    // ## TODO: Set up Supabase real-time subscriptions
    // const balanceSubscription = supabase
    //   .channel('wallet_balance_changes')
    //   .on('postgres_changes', {
    //     event: '*',
    //     schema: 'public',
    //     table: 'user_wallets',
    //     filter: `user_id=eq.${userId}`
    //   }, (payload) => {
    //     // Handle balance updates
    //   })
    //   .subscribe()

    // const transactionSubscription = supabase
    //   .channel('wallet_transaction_changes')
    //   .on('postgres_changes', {
    //     event: 'INSERT',
    //     schema: 'public',
    //     table: 'wallet_transactions',
    //     filter: `user_id=eq.${userId}`
    //   }, (payload) => {
    //     // Handle new transactions
    //   })
    //   .subscribe()

    // return () => {
    //   balanceSubscription.unsubscribe()
    //   transactionSubscription.unsubscribe()
    // }

    fetchWalletData()
  }, [userId])

  return {
    walletData,
    isLoading,
    error,
    refetch: fetchWalletData,
    updateCurrencyPreference,
    addTransaction,
    updateBalance
  }
}
