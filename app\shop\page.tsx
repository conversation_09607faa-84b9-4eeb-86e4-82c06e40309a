"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Zap, 
  ShoppingCart,
  Gamepad2,
  CreditCard,
  Gift
} from "lucide-react"

// ## Mock products data - will be replaced with Supabase queries
const mockProducts = [
  {
    id: "pubg-mobile-uc",
    title: "شحن يوسي PUBG Mobile",
    shortDescription: "شحن فوري لعملة UC في لعبة PUBG Mobile",
    image: "/images/products/pubg-mobile.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.8,
    reviewsCount: 1250,
    priceFrom: 25,
    estimatedTime: "أقل من دقيقة",
    popular: true,
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "free-fire-diamonds",
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire",
    image: "/images/products/free-fire.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.7,
    reviewsCount: 980,
    priceFrom: 30,
    estimatedTime: "أقل من دقيقة",
    features: ["شحن فوري", "دعم جميع المناطق", "أسعار مميزة"]
  },
  {
    id: "cod-mobile-cp",
    title: "شحن CP Call of Duty Mobile",
    shortDescription: "شحن فوري لنقاط COD في Call of Duty Mobile",
    image: "/images/products/cod-mobile.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.9,
    reviewsCount: 1450,
    priceFrom: 35,
    estimatedTime: "أقل من دقيقة",
    popular: true,
    features: ["شحن فوري", "دعم جميع السيرفرات", "ضمان الجودة"]
  },
  {
    id: "mobile-legends-diamonds",
    title: "شحن جواهر Mobile Legends",
    shortDescription: "شحن فوري للجواهر في Mobile Legends Bang Bang",
    image: "/images/products/mobile-legends.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.6,
    reviewsCount: 750,
    priceFrom: 28,
    estimatedTime: "أقل من دقيقة",
    features: ["شحن فوري", "دعم جميع المناطق", "خدمة عملاء ممتازة"]
  },
  {
    id: "clash-of-clans-gems",
    title: "شحن جواهر Clash of Clans",
    shortDescription: "شحن فوري للجواهر في Clash of Clans",
    image: "/images/products/clash-of-clans.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.8,
    reviewsCount: 1100,
    priceFrom: 40,
    estimatedTime: "أقل من دقيقة",
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "steam-wallet",
    title: "بطاقة Steam Wallet",
    shortDescription: "بطاقات شحن Steam للألعاب والمحتوى",
    image: "/images/products/steam.jpg",
    category: "بطاقات الألعاب",
    type: "standard",
    rating: 4.9,
    reviewsCount: 2100,
    priceFrom: 250,
    estimatedTime: "فوري",
    popular: true,
    features: ["تفعيل فوري", "صالحة عالمياً", "دعم فني متاح"]
  },
  {
    id: "google-play",
    title: "بطاقة Google Play",
    shortDescription: "بطاقات شحن Google Play للتطبيقات والألعاب",
    image: "/images/products/google-play.jpg",
    category: "بطاقات الألعاب",
    type: "standard",
    rating: 4.8,
    reviewsCount: 1580,
    priceFrom: 500,
    estimatedTime: "فوري",
    features: ["تفعيل فوري", "صالحة لجميع التطبيقات", "دعم فني متاح"]
  }
]

const categories = [
  { id: "all", label: "جميع المنتجات", icon: <Gift className="h-4 w-4" /> },
  { id: "ألعاب الموبايل", label: "ألعاب الموبايل", icon: <Gamepad2 className="h-4 w-4" /> },
  { id: "بطاقات الألعاب", label: "بطاقات الألعاب", icon: <CreditCard className="h-4 w-4" /> },
  { id: "اشتراكات الألعاب", label: "اشتراكات الألعاب", icon: <Star className="h-4 w-4" /> }
]

export default function ShopPage() {
  const [activeTab, setActiveTab] = useState("home")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else {
      setActiveTab(tab)
    }
  }

  // Product click handler
  const handleProductClick = (productId: string) => {
    router.push(`/shop/${productId}`)
  }

  // Filter products based on search and category
  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.shortDescription.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            متجر الألعاب
          </h1>
          <p className="text-slate-300 text-lg">
            اكتشف أفضل العروض لشحن ألعابك المفضلة
          </p>
        </div>

        {/* Search and Filters */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Search */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
              <Input
                placeholder="ابحث عن لعبتك المفضلة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400 focus:border-yellow-400"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="relative">
            <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full pr-10 pl-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:border-yellow-400 focus:outline-none appearance-none"
            >
              {categories.map((category) => (
                <option key={category.id} value={category.id} className="bg-slate-800">
                  {category.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Category Pills */}
        <div className="flex flex-wrap gap-3 mb-8">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 ${
                selectedCategory === category.id
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 border-0"
                  : "border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400"
              }`}
            >
              {category.icon}
              {category.label}
            </Button>
          ))}
        </div>

        {/* Products Grid - 2 products per row on all devices */}
        <div className="grid grid-cols-2 gap-4 md:gap-6">
          {filteredProducts.map((product) => (
            <Card
              key={product.id}
              onClick={() => handleProductClick(product.id)}
              className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 transition-all duration-300 cursor-pointer group overflow-hidden"
            >
              <CardContent className="p-0">
                {/* Product Image */}
                <div className="relative aspect-square">
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-slate-700 to-slate-800 flex items-center justify-center">
                      <Gamepad2 className="h-12 w-12 text-slate-400" />
                    </div>
                  )}

                  {/* Popular Badge */}
                  {product.popular && (
                    <div className="absolute top-3 right-3">
                      <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold">
                        الأكثر طلباً
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Product Name */}
                <div className="p-4">
                  <h3 className="text-white font-bold text-center group-hover:text-yellow-400 transition-colors">
                    {product.title}
                  </h3>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-bold text-white mb-2">لا توجد منتجات</h3>
            <p className="text-slate-400">
              لم نجد أي منتجات تطابق بحثك. جرب كلمات مختلفة أو اختر فئة أخرى.
            </p>
          </div>
        )}
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
