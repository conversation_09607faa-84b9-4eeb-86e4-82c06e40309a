"use client"

import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { Currency } from "@/lib/types"
import { CURRENCIES } from "@/lib/data/currencies"
import { cn } from "@/lib/utils"

interface CurrencySelectorProps {
  selectedCurrency: Currency
  onCurrencyChange: (currency: Currency) => void
  disabled?: boolean
  className?: string
}

export function CurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  disabled = false,
  className
}: CurrencySelectorProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {/* ## Currency selector - selection will be saved to Supabase user preferences */}
      <ToggleGroup
        type="single"
        value={selectedCurrency}
        onValueChange={(value) => {
          if (value && (value === "SDG" || value === "EGP")) {
            onCurrencyChange(value as Currency)
          }
        }}
        disabled={disabled}
        className="grid grid-cols-2 gap-3 w-full"
      >
        <ToggleGroupItem
          value="SDG"
          className={cn(
            "flex flex-col items-center justify-center gap-1 p-3 h-20 rounded-xl border-2 transition-all duration-300",
            "hover:scale-105 hover:shadow-lg min-w-0 flex-1",
            selectedCurrency === "SDG"
              ? "bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900 shadow-lg"
              : "bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:border-slate-500"
          )}
          disabled={disabled}
        >
          <div className="text-xl font-bold leading-none">
            {CURRENCIES.SDG.symbol}
          </div>
          <div className="text-xs font-medium text-center leading-tight px-1">
            {CURRENCIES.SDG.arabicName}
          </div>
        </ToggleGroupItem>

        <ToggleGroupItem
          value="EGP"
          className={cn(
            "flex flex-col items-center justify-center gap-1 p-3 h-20 rounded-xl border-2 transition-all duration-300",
            "hover:scale-105 hover:shadow-lg min-w-0 flex-1",
            selectedCurrency === "EGP"
              ? "bg-gradient-to-r from-yellow-400 to-orange-500 border-yellow-400 text-slate-900 shadow-lg"
              : "bg-slate-700/50 border-slate-600 text-slate-300 hover:bg-slate-600/50 hover:border-slate-500"
          )}
          disabled={disabled}
        >
          <div className="text-xl font-bold leading-none">
            {CURRENCIES.EGP.symbol}
          </div>
          <div className="text-xs font-medium text-center leading-tight px-1">
            {CURRENCIES.EGP.arabicName}
          </div>
        </ToggleGroupItem>
      </ToggleGroup>

      {/* Alternative Select-based Currency Selector (commented out) */}
      {/* 
      <Select value={selectedCurrency} onValueChange={onCurrencyChange} disabled={disabled}>
        <SelectTrigger className="w-full bg-slate-700/50 border-slate-600 text-white">
          <SelectValue placeholder="اختر العملة" />
        </SelectTrigger>
        <SelectContent className="bg-slate-800 border-slate-700">
          <SelectItem value="SDG" className="text-white hover:bg-slate-700">
            <div className="flex items-center gap-3">
              <span className="font-bold">{CURRENCIES.SDG.symbol}</span>
              <span>{CURRENCIES.SDG.arabicName}</span>
            </div>
          </SelectItem>
          <SelectItem value="EGP" className="text-white hover:bg-slate-700">
            <div className="flex items-center gap-3">
              <span className="font-bold">{CURRENCIES.EGP.symbol}</span>
              <span>{CURRENCIES.EGP.arabicName}</span>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
      */}
    </div>
  )
}
