import { Home, Store, Gift, User, Phone, Info, Wallet } from "lucide-react"
import { MenuItem } from "@/lib/types"

export const menuItems: MenuItem[] = [
  { icon: <Home className="h-5 w-5" />, label: "الرئيسية", href: "/" },
  { icon: <Store className="h-5 w-5" />, label: "المتجر", href: "/shop" },
  { icon: <Wallet className="h-5 w-5" />, label: "المحفظة", href: "/wallet" },
  { icon: <User className="h-5 w-5" />, label: "حسابي", href: "/profile" },
  { icon: <Gift className="h-5 w-5" />, label: "العروض", href: "#offers" },
  { icon: <Phone className="h-5 w-5" />, label: "اتصل بنا", href: "#contact" },
  { icon: <Info className="h-5 w-5" />, label: "من نحن", href: "#about" },
]
