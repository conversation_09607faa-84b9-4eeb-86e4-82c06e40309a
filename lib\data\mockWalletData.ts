import { WalletData, Transaction } from "@/lib/types"

// ## Mock data for wallet - Replace with Supabase queries
export const mockWalletData: WalletData = {
  balances: [
    {
      currency: "SDG",
      amount: 15000,
      lastUpdated: new Date()
    },
    {
      currency: "EGP", 
      amount: 250,
      lastUpdated: new Date()
    }
  ],
  selectedCurrency: "SDG",
  totalPurchases: 8500,
  transactions: [
    {
      id: "txn_001",
      type: "deposit",
      amount: 5000,
      currency: "SDG",
      description: "إيداع عبر فودافون كاش",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "VF123456789"
    },
    {
      id: "txn_002", 
      type: "purchase",
      amount: 1200,
      currency: "SDG",
      description: "شحن ببجي موبايل - 1800 UC",
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "PUB001"
    },
    {
      id: "txn_003",
      type: "deposit",
      amount: 100,
      currency: "EGP",
      description: "إيداع عبر فوري",
      date: new Date(Date.now() - 3 * 60 * 60 * 1000),
      status: "completed",
      reference: "FO987654321"
    },
    {
      id: "txn_004",
      type: "withdrawal",
      amount: 2000,
      currency: "SDG", 
      description: "سحب إلى محفظة خارجية",
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "WD445566"
    },
    {
      id: "txn_005",
      type: "purchase",
      amount: 800,
      currency: "SDG",
      description: "شحن فري فاير - 2200 ماسة",
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "FF002"
    }
  ]
}

// ## Helper function to filter transactions by type - Will be replaced with Supabase filtering
export function filterTransactionsByType(transactions: Transaction[], type?: "deposit" | "withdrawal" | "purchase"): Transaction[] {
  if (!type) return transactions
  return transactions.filter(transaction => transaction.type === type)
}

// ## Helper function to get balance for specific currency - Will be replaced with Supabase query
export function getBalanceForCurrency(walletData: WalletData, currency: string) {
  const balance = walletData.balances.find(b => b.currency === currency)
  return balance?.amount || 0
}
