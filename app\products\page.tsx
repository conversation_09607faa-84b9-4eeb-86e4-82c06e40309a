"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Zap, 
  ShoppingCart,
  Gamepad2,
  CreditCard,
  Gift
} from "lucide-react"

// ## Mock products data - will be replaced with Supabase queries
const mockProducts = [
  {
    id: "pubg-mobile-uc",
    title: "شحن يوسي PUBG Mobile",
    shortDescription: "شحن فوري لعملة UC في لعبة PUBG Mobile",
    image: "/images/products/pubg-mobile.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.8,
    reviewsCount: 1250,
    priceFrom: 25,
    estimatedTime: "أقل من دقيقة",
    popular: true,
    features: ["شحن فوري", "دعم جميع السيرفرات", "أسعار تنافسية"]
  },
  {
    id: "free-fire-diamonds",
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire",
    image: "/images/products/free-fire.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.7,
    reviewsCount: 980,
    priceFrom: 35,
    estimatedTime: "أقل من دقيقة",
    features: ["شحن فوري", "أسعار مخفضة", "دعم فني سريع"]
  },
  {
    id: "steam-wallet",
    title: "بطاقة Steam Wallet",
    shortDescription: "بطاقات شحن محفظة Steam للألعاب والمحتوى الرقمي",
    image: "/images/products/steam-wallet.jpg",
    category: "بطاقات الألعاب",
    type: "standard",
    rating: 4.9,
    reviewsCount: 2150,
    priceFrom: 2500,
    estimatedTime: "فوري",
    features: ["تفعيل فوري", "صالحة لجميع ألعاب Steam", "لا تنتهي صلاحيتها"]
  },
  {
    id: "playstation-plus",
    title: "اشتراك PlayStation Plus",
    shortDescription: "اشتراكات PlayStation Plus للاستمتاع بالألعاب المجانية",
    image: "/images/products/ps-plus.jpg",
    category: "اشتراكات الألعاب",
    type: "standard",
    rating: 4.6,
    reviewsCount: 850,
    priceFrom: 350,
    estimatedTime: "فوري",
    features: ["ألعاب مجانية شهرية", "خصومات حصرية", "اللعب الجماعي"]
  },
  {
    id: "cod-mobile-cp",
    title: "شحن CP Call of Duty Mobile",
    shortDescription: "شحن فوري لعملة CP في لعبة Call of Duty Mobile",
    image: "/images/products/cod-mobile.jpg",
    category: "ألعاب الموبايل",
    type: "instant",
    rating: 4.5,
    reviewsCount: 720,
    priceFrom: 30,
    estimatedTime: "أقل من دقيقة",
    features: ["شحن فوري", "دعم جميع المناطق", "ضمان الشحن"]
  },
  {
    id: "google-play",
    title: "بطاقة Google Play",
    shortDescription: "بطاقات شحن Google Play للتطبيقات والألعاب",
    image: "/images/products/google-play.jpg",
    category: "بطاقات الألعاب",
    type: "standard",
    rating: 4.8,
    reviewsCount: 1580,
    priceFrom: 500,
    estimatedTime: "فوري",
    features: ["تفعيل فوري", "صالحة لجميع التطبيقات", "دعم فني متاح"]
  }
]

const categories = [
  { id: "all", label: "جميع المنتجات", icon: <Gift className="h-4 w-4" /> },
  { id: "ألعاب الموبايل", label: "ألعاب الموبايل", icon: <Gamepad2 className="h-4 w-4" /> },
  { id: "بطاقات الألعاب", label: "بطاقات الألعاب", icon: <CreditCard className="h-4 w-4" /> },
  { id: "اشتراكات الألعاب", label: "اشتراكات الألعاب", icon: <Star className="h-4 w-4" /> }
]

export default function ShopPage() {
  const [activeTab, setActiveTab] = useState("home")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "products") {
      router.push("/products")
    } else if (tab === "home") {
      router.push("/")
    } else {
      setActiveTab(tab)
    }
  }

  // Filter products based on search and category
  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.shortDescription.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || product.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // Handle product click
  const handleProductClick = (productId: string) => {
    router.push(`/products/${productId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 pt-24 pb-32 max-w-7xl">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl lg:text-4xl font-bold text-white mb-2">
            متجر المنتجات
          </h1>
          <p className="text-slate-300">
            اكتشف مجموعة واسعة من الألعاب والبطاقات الرقمية
          </p>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-8 space-y-4">
          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
            <Input
              placeholder="ابحث عن المنتجات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-slate-800/50 border-slate-700/50 text-white pr-10 text-right"
            />
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className={`${
                  selectedCategory === category.id
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900"
                    : "bg-slate-800/50 border-slate-700/50 text-white hover:bg-slate-700/50"
                }`}
              >
                {category.icon}
                <span className="mr-2">{category.label}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card 
              key={product.id}
              className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 transition-all duration-300 cursor-pointer group"
              onClick={() => handleProductClick(product.id)}
            >
              <CardContent className="p-6">
                {/* Product Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-white font-bold text-lg group-hover:text-yellow-400 transition-colors">
                        {product.title}
                      </h3>
                      {product.popular && (
                        <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs">
                          الأكثر طلباً
                        </Badge>
                      )}
                    </div>
                    <p className="text-slate-300 text-sm mb-3">
                      {product.shortDescription}
                    </p>
                  </div>
                  <div className="text-2xl">
                    {product.type === "instant" ? "⚡" : "🎮"}
                  </div>
                </div>

                {/* Product Info */}
                <div className="space-y-3">
                  {/* Category and Rating */}
                  <div className="flex items-center justify-between text-sm">
                    <Badge variant="outline" className="border-slate-600 text-slate-300">
                      {product.category}
                    </Badge>
                    <div className="flex items-center gap-1 text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <span>{product.rating}</span>
                      <span className="text-slate-400">({product.reviewsCount})</span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="flex flex-wrap gap-1">
                    {product.features.slice(0, 2).map((feature, index) => (
                      <Badge 
                        key={index}
                        variant="secondary" 
                        className="bg-slate-700/50 text-slate-300 text-xs"
                      >
                        {feature}
                      </Badge>
                    ))}
                  </div>

                  {/* Price and Time */}
                  <div className="flex items-center justify-between pt-2 border-t border-slate-700/50">
                    <div className="text-right">
                      <div className="text-yellow-400 font-bold">
                        من {product.priceFrom.toLocaleString()} ج.س
                      </div>
                    </div>
                    <div className="flex items-center gap-1 text-slate-400 text-sm">
                      <Clock className="h-4 w-4" />
                      {product.estimatedTime}
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <Button 
                  className="w-full mt-4 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-medium"
                  onClick={(e) => {
                    e.stopPropagation()
                    handleProductClick(product.id)
                  }}
                >
                  {product.type === "instant" ? (
                    <>
                      <Zap className="h-4 w-4 ml-2" />
                      شحن فوري
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="h-4 w-4 ml-2" />
                      عرض التفاصيل
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-bold text-white mb-2">لا توجد منتجات</h3>
            <p className="text-slate-400">
              لم نجد أي منتجات تطابق بحثك. جرب كلمات مختلفة أو اختر فئة أخرى.
            </p>
          </div>
        )}
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
