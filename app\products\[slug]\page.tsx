import { notFound } from "next/navigation"
import { ProductStandard } from "@/components/products/ProductStandard"
import { ProductInstant } from "@/components/products/ProductInstant"
import { ProductPageWrapper } from "@/components/products/ProductPageWrapper"

// ## Mock product data - will be replaced with Supabase queries
const mockProducts = {
  "pubg-mobile-uc": {
    id: "pubg-mobile-uc",
    flow: "instant" as const,
    title: "شحن يوسي PUBG Mobile",
    shortDescription: "شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً",
    image: "/images/products/pubg-mobile.jpg",
    category: "ألعاب الموبايل",
    rating: 4.8,
    reviewsCount: 1250,
    estimatedTime: "أقل من دقيقة",
    packs: [
      {
        id: "uc-60",
        name: "60 UC",
        amount: "60 يوسي",
        price: 25,
        originalPrice: 30,
        discount: 17
      },
      {
        id: "uc-325",
        name: "325 UC",
        amount: "325 يوسي",
        price: 120,
        originalPrice: 150,
        discount: 20,
        popular: true
      },
      {
        id: "uc-660",
        name: "660 UC",
        amount: "660 يوسي",
        price: 240,
        originalPrice: 300,
        discount: 20
      },
      {
        id: "uc-1800",
        name: "1800 UC",
        amount: "1800 يوسي",
        price: 600,
        originalPrice: 750,
        discount: 20
      }
    ],
    servers: [
      { id: "global", name: "Global", region: "عالمي" },
      { id: "kr", name: "Korea", region: "كوريا" },
      { id: "vn", name: "Vietnam", region: "فيتنام" }
    ],
    instructions: [
      "تأكد من إدخال الأيدي الصحيح لتجنب فقدان الشحنة",
      "اختر السيرفر المناسب لحسابك",
      "الشحن يتم خلال دقائق معدودة",
      "في حالة وجود مشكلة تواصل مع الدعم الفني"
    ],
    features: [
      "شحن فوري خلال أقل من دقيقة",
      "دعم جميع السيرفرات",
      "أسعار تنافسية مع خصومات",
      "دعم فني متاح 24/7",
      "ضمان وصول الشحنة أو استرداد المبلغ"
    ]
  },
  "free-fire-diamonds": {
    id: "free-fire-diamonds",
    flow: "instant" as const,
    title: "شحن جواهر Free Fire",
    shortDescription: "شحن فوري للجواهر في لعبة Free Fire - احصل على الجواهر فوراً",
    image: "/images/products/free-fire.jpg",
    category: "ألعاب الموبايل",
    rating: 4.7,
    reviewsCount: 980,
    estimatedTime: "أقل من دقيقة",
    packs: [
      {
        id: "diamonds-100",
        name: "100 جوهرة",
        amount: "100 جوهرة",
        price: 35,
        originalPrice: 40,
        discount: 12
      },
      {
        id: "diamonds-310",
        name: "310 جوهرة",
        amount: "310 جوهرة",
        price: 100,
        originalPrice: 120,
        discount: 17,
        popular: true
      },
      {
        id: "diamonds-520",
        name: "520 جوهرة",
        amount: "520 جوهرة",
        price: 160,
        originalPrice: 200,
        discount: 20
      }
    ],
    servers: [
      { id: "global", name: "Global", region: "عالمي" },
      { id: "mena", name: "MENA", region: "الشرق الأوسط وشمال أفريقيا" }
    ],
    instructions: [
      "أدخل الأيدي الخاص بك بدقة",
      "تأكد من اختيار السيرفر الصحيح",
      "الشحن فوري ولا يحتاج انتظار",
      "احتفظ برقم المعاملة للمراجعة"
    ],
    features: [
      "شحن فوري خلال ثوانٍ",
      "أسعار مخفضة",
      "دعم فني سريع",
      "ضمان الشحن",
      "عروض وخصومات مستمرة"
    ]
  },
  "steam-wallet": {
    id: "steam-wallet",
    flow: "standard" as const,
    title: "بطاقة Steam Wallet",
    shortDescription: "بطاقات شحن محفظة Steam للألعاب والمحتوى الرقمي",
    fullDescription: "بطاقات Steam Wallet هي الطريقة المثالية لشحن محفظتك على منصة Steam وشراء الألعاب والمحتوى الإضافي. تتميز بطاقاتنا بالأمان والموثوقية مع التفعيل الفوري.",
    priceRange: "50 - 500",
    image: "/images/products/steam-wallet.jpg",
    category: "بطاقات الألعاب",
    rating: 4.9,
    reviewsCount: 2150,
    inStock: true,
    hasRegion: true,
    offers: [
      {
        id: "steam-50",
        label: "بطاقة 50 دولار",
        value: "50",
        price: 2500,
        originalPrice: 2700,
        discount: 7
      },
      {
        id: "steam-100",
        label: "بطاقة 100 دولار",
        value: "100",
        price: 4800,
        originalPrice: 5200,
        discount: 8
      },
      {
        id: "steam-200",
        label: "بطاقة 200 دولار",
        value: "200",
        price: 9200,
        originalPrice: 10000,
        discount: 8
      }
    ],
    regions: [
      { id: "global", label: "عالمي", value: "global" },
      { id: "us", label: "الولايات المتحدة", value: "us" },
      { id: "eu", label: "أوروبا", value: "eu" }
    ],
    features: [
      "تفعيل فوري للبطاقة",
      "صالحة لجميع ألعاب Steam",
      "لا تنتهي صلاحيتها",
      "دعم فني متخصص",
      "ضمان صحة البطاقة"
    ],
    additionalInfo: {
      "نوع البطاقة": "رقمية",
      "طريقة التسليم": "البريد الإلكتروني",
      "وقت التسليم": "فوري",
      "المنطقة": "عالمي",
      "العملة": "دولار أمريكي"
    }
  },
  "playstation-plus": {
    id: "playstation-plus",
    flow: "standard" as const,
    title: "اشتراك PlayStation Plus",
    shortDescription: "اشتراكات PlayStation Plus للاستمتاع بالألعاب المجانية والمزايا الحصرية",
    fullDescription: "احصل على اشتراك PlayStation Plus واستمتع بمجموعة ضخمة من الألعاب المجانية الشهرية، والخصومات الحصرية، واللعب الجماعي عبر الإنترنت، والمزيد من المزايا الرائعة.",
    priceRange: "200 - 1500",
    image: "/images/products/ps-plus.jpg",
    category: "اشتراكات الألعاب",
    rating: 4.6,
    reviewsCount: 850,
    inStock: true,
    hasRegion: true,
    offers: [
      {
        id: "ps-plus-1m",
        label: "شهر واحد",
        value: "1",
        price: 350,
        originalPrice: 400,
        discount: 12
      },
      {
        id: "ps-plus-3m",
        label: "3 أشهر",
        value: "3",
        price: 950,
        originalPrice: 1100,
        discount: 14
      },
      {
        id: "ps-plus-12m",
        label: "12 شهر",
        value: "12",
        price: 3200,
        originalPrice: 3800,
        discount: 16
      }
    ],
    regions: [
      { id: "us", label: "الولايات المتحدة", value: "us" },
      { id: "eu", label: "أوروبا", value: "eu" },
      { id: "mena", label: "الشرق الأوسط", value: "mena" }
    ],
    features: [
      "ألعاب مجانية شهرية",
      "خصومات حصرية على الألعاب",
      "اللعب الجماعي عبر الإنترنت",
      "تخزين سحابي للحفظ",
      "وصول مبكر للألعاب الجديدة"
    ],
    additionalInfo: {
      "نوع الاشتراك": "رقمي",
      "طريقة التفعيل": "كود التفعيل",
      "وقت التسليم": "فوري",
      "التجديد": "يدوي",
      "الإلغاء": "في أي وقت"
    }
  }
}

interface ProductPageProps {
  params: {
    slug: string
  }
}

export default function ProductPage({ params }: ProductPageProps) {
  // ## Get product data - will be replaced with Supabase query
  const product = mockProducts[params.slug as keyof typeof mockProducts]

  if (!product) {
    notFound()
  }

  // Render appropriate component based on product flow
  if (product.flow === "instant") {
    return (
      <ProductPageWrapper>
        <ProductInstant
          product={product}
        />
      </ProductPageWrapper>
    )
  }

  return (
    <ProductPageWrapper>
      <ProductStandard
        product={product}
      />
    </ProductPageWrapper>
  )
}

// ## Generate static params for known products - will be replaced with Supabase query
export async function generateStaticParams() {
  return Object.keys(mockProducts).map((slug) => ({
    slug,
  }))
}
