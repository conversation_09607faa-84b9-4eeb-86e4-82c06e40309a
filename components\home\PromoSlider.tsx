"use client"

import { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { slides } from "@/lib/data/slides"

export function PromoSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)

  // Auto-slide functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
  }

  return (
    <section className="relative rounded-3xl overflow-hidden shadow-2xl">
      <div className="relative h-64 lg:h-80">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-transform duration-500 ease-in-out ${
              index === currentSlide
                ? "translate-x-0"
                : index < currentSlide
                  ? "-translate-x-full"
                  : "translate-x-full"
            }`}
          >
            <div className={`h-full bg-gradient-to-r ${slide.gradient} p-6 lg:p-8`}>
              <div className="flex flex-col lg:flex-row items-center gap-6 lg:gap-8 h-full">
                {/* Game Thumbnails */}
                <div className="grid grid-cols-3 gap-3 lg:gap-4 flex-1 lg:max-w-xs">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div
                      key={i}
                      className="w-16 h-16 lg:w-20 lg:h-20 bg-white/80 backdrop-blur-sm rounded-xl flex items-center justify-center text-slate-600 text-lg lg:text-xl font-bold shadow-lg hover:scale-110 transition-transform duration-300 cursor-pointer"
                    >
                      🎮
                    </div>
                  ))}
                </div>

                {/* CTA Content */}
                <div className="flex-1 text-center lg:text-right">
                  <h2 className="text-slate-900 font-bold text-2xl lg:text-4xl mb-4 leading-tight">
                    {slide.title}
                  </h2>
                  <p className="text-slate-700 text-lg lg:text-xl mb-6">{slide.subtitle} ⚡</p>
                  <Button className="bg-slate-900 hover:bg-slate-800 text-white text-lg lg:text-xl px-8 lg:px-12 py-4 lg:py-6 rounded-full font-semibold shadow-lg hover:scale-105 transition-all duration-300">
                    {slide.buttonText}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Slider Controls */}
      <button
        onClick={prevSlide}
        className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 lg:p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
      >
        <ChevronLeft className="h-5 w-5 lg:h-6 lg:w-6" />
      </button>

      <button
        onClick={nextSlide}
        className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 lg:p-3 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
      >
        <ChevronRight className="h-5 w-5 lg:h-6 lg:w-6" />
      </button>

      {/* Slider Indicators */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide ? "bg-white shadow-lg" : "bg-white/50 hover:bg-white/70"
            }`}
          />
        ))}
      </div>
    </section>
  )
}
