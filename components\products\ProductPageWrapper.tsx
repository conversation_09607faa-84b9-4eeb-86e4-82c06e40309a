"use client"

import { useState } from "react"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { useRouter } from "next/navigation"

interface ProductPageWrapperProps {
  children: React.ReactNode
}

export function ProductPageWrapper({ children }: ProductPageWrapperProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("shop") // Set to shop since this is a product page
  const router = useRouter()

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="relative">
      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />
      
      {children}
      
      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
